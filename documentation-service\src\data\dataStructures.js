export const dataStructures = {
  assessmentData: {
    title: "Assessment Data Structure",
    description: "Struktur lengkap data assessment yang dikirim user untuk dianalisis AI",
    overview: "Assessment data terdiri dari 3 komponen utama: RIASEC (Holland Code), OCEAN (Big Five), dan VIA-<PERSON> (Character Strengths). Semua skor menggunakan skala 0-100.",
    
    components: {
      riasec: {
        name: "RIASEC Assessment (Holland Code)",
        description: "Model kepribadian kerja yang mengidentifikasi 6 tipe kepribadian berdasarkan minat dan preferensi kerja",
        fields: {
          realistic: {
            range: "0-100",
            description: "Kecenderungan pada pekerjaan praktis, hands-on, bekerja dengan alat dan mesin",
            characteristics: ["Prak<PERSON>", "Teknis", "Fisik", "Konkret"]
          },
          investigative: {
            range: "0-100", 
            description: "Kecenderungan pada penelitian, analisis, dan pemecahan masalah kompleks",
            characteristics: ["Analitis", "Intelektual", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]
          },
          artistic: {
            range: "0-100",
            description: "Kecenderungan pada kreativitas, ekspresi diri, dan aktivitas artistik",
            characteristics: ["Kreatif", "Ekspresif", "Imajinatif", "Orisinal"]
          },
          social: {
            range: "0-100",
            description: "Kecenderungan pada interaksi sosial, membantu orang, dan aktivitas interpersonal",
            characteristics: ["Sosial", "Membantu", "Komunikatif", "Empati"]
          },
          enterprising: {
            range: "0-100",
            description: "Kecenderungan pada kepemimpinan, bisnis, dan aktivitas persuasif",
            characteristics: ["Ambisius", "Persuasif", "Kompetitif", "Energik"]
          },
          conventional: {
            range: "0-100",
            description: "Kecenderungan pada organisasi, detail, dan aktivitas terstruktur",
            characteristics: ["Terorganisir", "Detail", "Sistematis", "Efisien"]
          }
        }
      },
      
      ocean: {
        name: "OCEAN (Big Five Personality Traits)",
        description: "Model kepribadian yang mengukur 5 dimensi utama kepribadian manusia",
        fields: {
          openness: {
            range: "0-100",
            description: "Keterbukaan terhadap pengalaman baru, ide, dan perubahan",
            highScore: "Kreatif, imajinatif, suka petualangan, terbuka pada ide baru",
            lowScore: "Konservatif, praktis, lebih suka rutinitas, tradisional"
          },
          conscientiousness: {
            range: "0-100",
            description: "Kehati-hatian, kedisiplinan, dan orientasi pada tujuan",
            highScore: "Disiplin, terorganisir, dapat diandalkan, berorientasi tujuan",
            lowScore: "Spontan, fleksibel, santai, kurang terstruktur"
          },
          extraversion: {
            range: "0-100",
            description: "Orientasi sosial, energi, dan assertiveness",
            highScore: "Sosial, energik, asertif, suka berinteraksi",
            lowScore: "Introvert, tenang, reflektif, lebih suka kesendirian"
          },
          agreeableness: {
            range: "0-100",
            description: "Kecenderungan kooperatif, empati, dan kepercayaan pada orang lain",
            highScore: "Kooperatif, empati, percaya pada orang, altruistik",
            lowScore: "Kompetitif, skeptis, asertif, independen"
          },
          neuroticism: {
            range: "0-100",
            description: "Stabilitas emosional dan ketahanan terhadap stress (skor tinggi = kurang stabil)",
            highScore: "Mudah cemas, sensitif terhadap stress, emosional",
            lowScore: "Tenang, stabil secara emosional, tahan stress"
          }
        }
      },
      
      viaIs: {
        name: "VIA-IS Character Strengths",
        description: "24 kekuatan karakter yang dikelompokkan dalam 6 virtue categories",
        categories: {
          wisdom: {
            name: "Wisdom and Knowledge",
            strengths: {
              creativity: "Kreativitas dan originalitas dalam berpikir",
              curiosity: "Rasa ingin tahu dan minat untuk belajar",
              judgment: "Kemampuan berpikir kritis dan open-minded",
              loveOfLearning: "Cinta belajar dan menguasai keterampilan baru",
              perspective: "Kebijaksanaan dan kemampuan memberikan nasihat"
            }
          },
          courage: {
            name: "Courage",
            strengths: {
              bravery: "Keberanian menghadapi tantangan dan kesulitan",
              perseverance: "Ketekunan dan daya tahan dalam mencapai tujuan",
              honesty: "Kejujuran dan autentisitas dalam berperilaku",
              zest: "Antusiasme dan semangat hidup"
            }
          },
          humanity: {
            name: "Humanity",
            strengths: {
              love: "Kemampuan mencintai dan dicintai",
              kindness: "Kebaikan dan kemurahan hati",
              socialIntelligence: "Kecerdasan sosial dan pemahaman interpersonal"
            }
          },
          justice: {
            name: "Justice",
            strengths: {
              teamwork: "Kemampuan bekerja dalam tim dan citizenship",
              fairness: "Keadilan dan perlakuan setara",
              leadership: "Kemampuan memimpin dan mengorganisir"
            }
          },
          temperance: {
            name: "Temperance",
            strengths: {
              forgiveness: "Kemampuan memaafkan dan memberikan kesempatan kedua",
              humility: "Kerendahan hati dan tidak sombong",
              prudence: "Kehati-hatian dalam mengambil keputusan",
              selfRegulation: "Kontrol diri dan disiplin"
            }
          },
          transcendence: {
            name: "Transcendence",
            strengths: {
              appreciationOfBeauty: "Apresiasi terhadap keindahan dan keunggulan",
              gratitude: "Rasa syukur dan penghargaan",
              hope: "Optimisme dan harapan untuk masa depan",
              humor: "Humor dan kemampuan melihat sisi positif",
              spirituality: "Spiritualitas dan pencarian makna hidup"
            }
          }
        }
      }
    },
    
    example: {
      riasec: {
        realistic: 75,
        investigative: 85,
        artistic: 65,
        social: 70,
        enterprising: 80,
        conventional: 60
      },
      ocean: {
        openness: 88,
        conscientiousness: 75,
        extraversion: 72,
        agreeableness: 85,
        neuroticism: 35
      },
      viaIs: {
        creativity: 82,
        curiosity: 90,
        judgment: 78,
        loveOfLearning: 95,
        perspective: 75,
        bravery: 68,
        perseverance: 85,
        honesty: 88,
        zest: 76,
        love: 82,
        kindness: 87,
        socialIntelligence: 74,
        teamwork: 79,
        fairness: 86,
        leadership: 72,
        forgiveness: 77,
        humility: 81,
        prudence: 73,
        selfRegulation: 84,
        appreciationOfBeauty: 69,
        gratitude: 89,
        hope: 83,
        humor: 71,
        spirituality: 58
      },
      assessmentName: "AI-Driven Talent Mapping"
    },
    
    validation: {
      rules: [
        "Semua skor harus berupa integer antara 0-100",
        "RIASEC: 6 dimensi wajib diisi",
        "OCEAN: 5 dimensi wajib diisi", 
        "VIA-IS: 24 kekuatan karakter wajib diisi",
        "assessmentName: opsional, harus salah satu dari: 'AI-Driven Talent Mapping', 'AI-Based IQ Test', 'Custom Assessment'"
      ],
      totalFields: "35 field wajib (6 RIASEC + 5 OCEAN + 24 VIA-IS)"
    }
  },

  personaProfile: {
    title: "Persona Profile Structure",
    description: "Struktur lengkap profil persona yang dihasilkan AI berdasarkan analisis assessment data",
    overview: "Persona profile adalah hasil analisis komprehensif yang memberikan gambaran mendalam tentang kepribadian, kekuatan, kelemahan, dan rekomendasi pengembangan karir seseorang.",

    fields: {
      archetype: {
        type: "String",
        required: true,
        description: "Nama archetype yang paling sesuai dengan persona",
        examples: ["The Analytical Innovator", "The Creative Collaborator", "The Strategic Leader", "The Empathetic Helper"],
        maxLength: 100
      },

      shortSummary: {
        type: "String",
        required: true,
        description: "Ringkasan singkat tentang persona dalam 1-2 paragraf",
        purpose: "Memberikan gambaran umum kepribadian dan karakteristik utama",
        minLength: 10,
        maxLength: 1000
      },

      strengthSummary: {
        type: "String",
        required: true,
        description: "Ringkasan kekuatan utama persona dalam 1 paragraf",
        purpose: "Menjelaskan secara naratif kekuatan-kekuatan yang dimiliki",
        maxLength: 500
      },

      strengths: {
        type: "Array[String]",
        required: true,
        description: "Daftar 3-5 kekuatan/strength utama dari persona",
        minItems: 3,
        maxItems: 5,
        examples: [
          "Kemampuan analisis yang tajam",
          "Kreativitas dan inovasi",
          "Keingintahuan intelektual yang tinggi",
          "Kemampuan belajar mandiri yang kuat"
        ]
      },

      weaknessSummary: {
        type: "String",
        required: true,
        description: "Ringkasan kelemahan utama persona dalam 1 paragraf",
        purpose: "Menjelaskan area yang perlu diperhatikan dan dikembangkan",
        maxLength: 500
      },

      weaknesses: {
        type: "Array[String]",
        required: true,
        description: "Daftar 3-5 kelemahan yang perlu diperhatikan",
        minItems: 3,
        maxItems: 5,
        examples: [
          "Kecenderungan perfeksionis",
          "Kurang sabar dengan detail administratif",
          "Perlu mengembangkan keterampilan komunikasi"
        ]
      },

      careerRecommendation: {
        type: "Array[Object]",
        required: true,
        description: "Daftar 3-5 rekomendasi karir dengan analisis prospek",
        minItems: 3,
        maxItems: 5,
        structure: {
          careerName: {
            type: "String",
            description: "Nama profesi/karir yang direkomendasikan",
            examples: ["Data Scientist", "Software Engineer", "Product Manager"]
          },
          careerProspect: {
            type: "Object",
            description: "Analisis prospek karir dalam 5 dimensi",
            fields: {
              jobAvailability: {
                type: "String",
                values: ["super high", "high", "moderate", "low", "super low"],
                description: "Ketersediaan lowongan kerja di pasar"
              },
              salaryPotential: {
                type: "String",
                values: ["super high", "high", "moderate", "low", "super low"],
                description: "Potensi gaji dan kompensasi"
              },
              careerProgression: {
                type: "String",
                values: ["super high", "high", "moderate", "low", "super low"],
                description: "Jalur dan peluang kemajuan karir"
              },
              industryGrowth: {
                type: "String",
                values: ["super high", "high", "moderate", "low", "super low"],
                description: "Pertumbuhan industri terkait profesi"
              },
              skillDevelopment: {
                type: "String",
                values: ["super high", "high", "moderate", "low", "super low"],
                description: "Peluang mengembangkan keahlian di profesi ini"
              }
            }
          }
        }
      },

      insights: {
        type: "Array[String]",
        required: true,
        description: "Daftar 3-5 insight atau saran pengembangan diri",
        minItems: 3,
        maxItems: 5,
        purpose: "Memberikan panduan strategis untuk pengembangan personal",
        examples: [
          "Fokus pada proyek yang menggabungkan analisis data dengan kreativitas",
          "Kembangkan kemampuan storytelling untuk mengkomunikasikan temuan analitis",
          "Cari mentor yang dapat membantu mengembangkan soft skills"
        ]
      },

      skillSuggestion: {
        type: "Array[String]",
        required: true,
        description: "Rekomendasi 3-5 skill untuk dikembangkan jangka pendek dan menengah",
        minItems: 3,
        maxItems: 5,
        examples: [
          "Machine Learning dan AI",
          "Data Visualization",
          "Public Speaking",
          "Project Management"
        ]
      },

      possiblePitfalls: {
        type: "Array[String]",
        required: true,
        description: "Daftar 2-5 kesalahan atau jebakan karir yang perlu diwaspadai",
        minItems: 2,
        maxItems: 5,
        examples: [
          "Terjebak dalam analysis paralysis",
          "Mengabaikan aspek bisnis dari solusi teknis",
          "Kurang membangun network profesional"
        ]
      },

      riskTolerance: {
        type: "String",
        required: true,
        description: "Seberapa tinggi toleransi risiko persona dalam karir dan pekerjaan",
        values: ["very high", "high", "moderate", "low", "very low"],
        purpose: "Membantu dalam pengambilan keputusan karir dan investasi"
      },

      workEnvironment: {
        type: "String",
        required: true,
        description: "Deskripsi lingkungan kerja yang ideal untuk persona",
        purpose: "Membantu dalam memilih perusahaan dan budaya kerja yang sesuai",
        maxLength: 500
      },

      roleModel: {
        type: "Array[String]",
        required: true,
        description: "Daftar 2-3 role model yang relevan dan inspiratif",
        minItems: 2,
        maxItems: 3,
        examples: ["Marie Curie", "Albert Einstein", "B.J. Habibie"]
      }
    },

    example: {
      archetype: "The Analytical Innovator",
      shortSummary: "Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi. Kombinasi antara kecerdasan logis-matematis dan keterbukaan terhadap pengalaman baru membuat Anda unggul dalam memecahkan masalah kompleks dengan pendekatan inovatif.",
      strengthSummary: "Kekuatan utama Anda terletak pada analisis mendalam, kreativitas, dan dorongan kuat untuk belajar hal baru. Ini membuat Anda mampu menghasilkan solusi unik di berbagai situasi kompleks.",
      strengths: [
        "Kemampuan analisis yang tajam",
        "Kreativitas dan inovasi",
        "Keingintahuan intelektual yang tinggi",
        "Kemampuan belajar mandiri yang kuat"
      ],
      weaknessSummary: "Area yang perlu diperhatikan adalah kecenderungan perfeksionis yang dapat menghambat produktivitas, serta kebutuhan untuk mengembangkan keterampilan komunikasi dan kepemimpinan.",
      weaknesses: [
        "Kecenderungan perfeksionis",
        "Kurang sabar dengan detail administratif",
        "Perlu mengembangkan keterampilan komunikasi"
      ],
      careerRecommendation: [
        {
          careerName: "Data Scientist",
          careerProspect: {
            jobAvailability: "high",
            salaryPotential: "high",
            careerProgression: "high",
            industryGrowth: "super high",
            skillDevelopment: "super high"
          }
        },
        {
          careerName: "Software Engineer",
          careerProspect: {
            jobAvailability: "super high",
            salaryPotential: "high",
            careerProgression: "high",
            industryGrowth: "super high",
            skillDevelopment: "super high"
          }
        },
        {
          careerName: "Research Scientist",
          careerProspect: {
            jobAvailability: "moderate",
            salaryPotential: "moderate",
            careerProgression: "moderate",
            industryGrowth: "moderate",
            skillDevelopment: "high"
          }
        }
      ],
      insights: [
        "Fokus pada proyek yang menggabungkan analisis data dengan kreativitas",
        "Kembangkan kemampuan storytelling untuk mengkomunikasikan temuan analitis",
        "Cari mentor yang dapat membantu mengembangkan soft skills"
      ],
      skillSuggestion: [
        "Machine Learning dan AI",
        "Data Visualization",
        "Public Speaking",
        "Project Management"
      ],
      possiblePitfalls: [
        "Terjebak dalam analysis paralysis",
        "Mengabaikan aspek bisnis dari solusi teknis",
        "Kurang membangun network profesional"
      ],
      riskTolerance: "moderate",
      workEnvironment: "Lingkungan kerja yang memberikan otonomi intelektual, menghargai inovasi, dan menyediakan tantangan kognitif yang berkelanjutan. Anda berkembang di tempat yang terstruktur namun fleksibel.",
      roleModel: [
        "Marie Curie",
        "Albert Einstein",
        "B.J. Habibie"
      ]
    }
  },

  websocketNotifications: {
    title: "WebSocket Notification Structures",
    description: "Struktur lengkap notifikasi real-time yang dikirim melalui WebSocket",
    overview: "WebSocket notifications memberikan update real-time tentang status pemrosesan assessment kepada user yang sedang terhubung.",

    connectionFlow: [
      "1. Client connect ke WebSocket server",
      "2. Client emit 'authenticate' event dengan JWT token",
      "3. Server respond dengan 'authenticated' atau 'auth_error'",
      "4. Jika authenticated, client akan menerima notification events",
      "5. Handle disconnect dan reconnection dengan re-authentication"
    ],

    events: {
      "analysis-started": {
        description: "Dikirim ketika analisis assessment dimulai",
        timing: "Segera setelah job assessment masuk ke queue dan mulai diproses",
        structure: {
          jobId: "String (UUID) - ID unik job assessment",
          status: "String - Selalu 'started'",
          message: "String - Pesan user-friendly",
          metadata: {
            assessmentName: "String - Nama assessment",
            estimatedProcessingTime: "String - Estimasi waktu pemrosesan"
          },
          timestamp: "String (ISO 8601) - Waktu event dikirim"
        },
        example: {
          jobId: "550e8400-e29b-41d4-a716-************",
          status: "started",
          message: "Your analysis has started processing...",
          metadata: {
            assessmentName: "AI-Driven Talent Mapping",
            estimatedProcessingTime: "5-10 minutes"
          },
          timestamp: "2024-01-01T12:00:00.000Z"
        }
      },

      "analysis-complete": {
        description: "Dikirim ketika analisis assessment selesai dengan sukses",
        timing: "Setelah AI berhasil menganalisis dan menyimpan hasil ke database",
        structure: {
          jobId: "String (UUID) - ID job assessment yang selesai",
          resultId: "String (UUID) - ID hasil yang dapat digunakan untuk GET /api/archive/results/:id",
          status: "String - Selalu 'completed'",
          message: "String - Pesan user-friendly",
          metadata: {
            assessmentName: "String - Nama assessment",
            processingTime: "String - Waktu aktual pemrosesan"
          },
          timestamp: "String (ISO 8601) - Waktu event dikirim"
        },
        example: {
          jobId: "550e8400-e29b-41d4-a716-************",
          resultId: "660e8400-e29b-41d4-a716-************",
          status: "completed",
          message: "Your analysis is ready!",
          metadata: {
            assessmentName: "AI-Driven Talent Mapping",
            processingTime: "7 minutes"
          },
          timestamp: "2024-01-01T12:07:00.000Z"
        },
        nextActions: [
          "Gunakan resultId untuk fetch hasil lengkap",
          "Tampilkan notifikasi sukses ke user",
          "Redirect ke halaman hasil atau update UI"
        ]
      },

      "analysis-failed": {
        description: "Dikirim ketika analisis assessment gagal",
        timing: "Ketika terjadi error dalam pemrosesan assessment",
        structure: {
          jobId: "String (UUID) - ID job assessment yang gagal",
          error: "String - Pesan error teknis untuk debugging",
          message: "String - Pesan user-friendly",
          metadata: {
            assessmentName: "String - Nama assessment",
            errorType: "String - Kategori error"
          },
          timestamp: "String (ISO 8601) - Waktu event dikirim"
        },
        errorTypes: [
          "PROCESSING_ERROR - Error dalam pemrosesan AI",
          "TIMEOUT_ERROR - Timeout dalam pemrosesan",
          "VALIDATION_ERROR - Error validasi data",
          "SERVICE_UNAVAILABLE - Service AI tidak tersedia",
          "QUOTA_EXCEEDED - Quota pemrosesan terlampaui"
        ],
        example: {
          jobId: "550e8400-e29b-41d4-a716-************",
          error: "AI service temporarily unavailable",
          message: "Analysis failed. Please try again.",
          metadata: {
            assessmentName: "AI-Driven Talent Mapping",
            errorType: "SERVICE_UNAVAILABLE"
          },
          timestamp: "2024-01-01T12:05:00.000Z"
        },
        nextActions: [
          "Tampilkan pesan error yang user-friendly",
          "Berikan opsi untuk retry assessment",
          "Log error untuk monitoring dan debugging"
        ]
      }
    }
  }
};
